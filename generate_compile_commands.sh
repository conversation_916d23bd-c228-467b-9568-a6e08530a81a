#!/bin/bash

# Script to generate compile_commands.json for clangd using bear
# This script wraps the normal build process to capture compilation commands

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Generating compile_commands.json for clangd ===${NC}"

# Check if bear is installed
if ! command -v bear &> /dev/null; then
    echo -e "${RED}Error: bear is not installed. Please install it first:${NC}"
    echo "  sudo apt install bear"
    exit 1
fi

# Get the project root directory
PROJECT_ROOT=$(pwd)
echo -e "${YELLOW}Project root: ${PROJECT_ROOT}${NC}"

# Check if we're in the right directory
if [ ! -f "build/envsetup.sh" ] || [ ! -f "build.sh" ]; then
    echo -e "${RED}Error: This script must be run from the project root directory${NC}"
    echo "Expected files: build/envsetup.sh, build.sh"
    exit 1
fi

# Source the environment setup
echo -e "${YELLOW}Sourcing environment setup...${NC}"
source build/envsetup.sh

# Clean previous build to ensure all files are compiled
echo -e "${YELLOW}Cleaning previous build...${NC}"
./build.sh clean

# Remove any existing compile_commands.json (unless appending)
if [ -f "compile_commands.json" ] && [ "${BUILD_CHOICE}" != "4" ]; then
    echo -e "${YELLOW}Removing existing compile_commands.json${NC}"
    rm -f compile_commands.json
fi

# Check if compile_commands.json already exists
EXISTING_FILE=""
if [ -f "compile_commands.json" ]; then
    EXISTING_COUNT=$(grep -c '"file":' compile_commands.json 2>/dev/null || echo "0")
    if [ ${EXISTING_COUNT} -gt 0 ]; then
        EXISTING_FILE="(existing file has ${EXISTING_COUNT} entries)"
        echo -e "${YELLOW}Found existing compile_commands.json with ${EXISTING_COUNT} entries${NC}"
    fi
fi

# Ask user what to build
echo -e "${YELLOW}What would you like to build?${NC}"
echo "1) Full build (kernel + buildroot + platform) - takes longer but captures everything"
echo "2) Kernel only - faster, captures kernel and driver compilation"
echo "3) Platform only - captures userspace application compilation"
if [ -n "${EXISTING_FILE}" ]; then
    echo "4) Append platform to existing file ${EXISTING_FILE}"
fi
read -p "Enter your choice (1-$([ -n "${EXISTING_FILE}" ] && echo "4" || echo "3")) [default: 2]: " BUILD_CHOICE

case "${BUILD_CHOICE:-2}" in
    1)
        echo -e "${YELLOW}Running full build with bear...${NC}"
        bear -o "${PROJECT_ROOT}/compile_commands.json" --append ./build.sh
        ;;
    2)
        echo -e "${YELLOW}Running kernel build with bear...${NC}"
        bear -o "${PROJECT_ROOT}/compile_commands.json" --append ./build.sh kernel
        ;;
    3)
        echo -e "${YELLOW}Running platform build with bear...${NC}"
        # First build kernel and buildroot dependencies
        ./build.sh kernel
        ./build.sh buildroot
        # Then capture platform compilation (multiple components)
        echo -e "${YELLOW}Capturing platform Makefile compilation...${NC}"
        bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform INSTALL_FILES

        # Capture auto framework compilation
        if [ -f "platform/framework/auto/build.sh" ]; then
            echo -e "${YELLOW}Capturing auto framework compilation...${NC}"
            bear -o "${PROJECT_ROOT}/compile_commands.json" --append platform/framework/auto/build.sh
        fi

        # Capture SDK lib compilation
        if [ -d "platform/framework/auto/sdk_lib" ]; then
            echo -e "${YELLOW}Capturing SDK lib compilation...${NC}"
            bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_lib
        fi

        # Capture SDK demo compilation
        if [ -d "platform/framework/auto/sdk_demo" ]; then
            echo -e "${YELLOW}Capturing SDK demo compilation...${NC}"
            bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_demo
        fi
        ;;
    4)
        if [ -f "compile_commands.json" ]; then
            echo -e "${YELLOW}Appending platform build to existing compile_commands.json...${NC}"
            # Ensure dependencies are built
            echo -e "${YELLOW}Checking kernel and buildroot dependencies...${NC}"
            if [ ! -d "out" ] || [ ! -f "out/*/*/buildroot/target/usr/bin/gcc" ]; then
                echo -e "${YELLOW}Building required dependencies...${NC}"
                ./build.sh kernel
                ./build.sh buildroot
            fi

            # Capture platform compilation and append (multiple components)
            echo -e "${YELLOW}Appending platform Makefile compilation...${NC}"
            bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform INSTALL_FILES

            # Append auto framework compilation
            if [ -f "platform/framework/auto/build.sh" ]; then
                echo -e "${YELLOW}Appending auto framework compilation...${NC}"
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append platform/framework/auto/build.sh
            fi

            # Append SDK lib compilation
            if [ -d "platform/framework/auto/sdk_lib" ]; then
                echo -e "${YELLOW}Appending SDK lib compilation...${NC}"
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_lib
            fi

            # Append SDK demo compilation
            if [ -d "platform/framework/auto/sdk_demo" ]; then
                echo -e "${YELLOW}Appending SDK demo compilation...${NC}"
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_demo
            fi
        else
            echo -e "${RED}No existing compile_commands.json found. Running platform build instead.${NC}"
            ./build.sh kernel
            ./build.sh buildroot
            bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform INSTALL_FILES

            # Also capture the additional platform components
            if [ -f "platform/framework/auto/build.sh" ]; then
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append platform/framework/auto/build.sh
            fi
            if [ -d "platform/framework/auto/sdk_lib" ]; then
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_lib
            fi
            if [ -d "platform/framework/auto/sdk_demo" ]; then
                bear -o "${PROJECT_ROOT}/compile_commands.json" --append make -C platform/framework/auto/sdk_demo
            fi
        fi
        ;;
    *)
        echo -e "${RED}Invalid choice. Defaulting to kernel build.${NC}"
        bear -o "${PROJECT_ROOT}/compile_commands.json" --append ./build.sh kernel
        ;;
esac

# Check if compile_commands.json was generated
if [ ! -f "compile_commands.json" ]; then
    echo -e "${RED}Error: compile_commands.json was not generated${NC}"
    echo "This might happen if the build failed before any compilation started."
    exit 1
fi

# Get statistics about the generated file
COMPILE_COMMANDS_COUNT=$(grep -c '"file":' compile_commands.json 2>/dev/null || echo "unknown")
FILE_SIZE=$(du -h compile_commands.json | cut -f1)
LINE_COUNT=$(wc -l < compile_commands.json)

echo -e "${GREEN}=== Success! ===${NC}"
echo -e "${GREEN}compile_commands.json generated successfully${NC}"
echo -e "Location: ${PROJECT_ROOT}/compile_commands.json"
echo -e "File size: ${FILE_SIZE}"
echo -e "Total lines: ${LINE_COUNT}"
echo -e "Number of compilation units: ${COMPILE_COMMANDS_COUNT}"

# Verify the content briefly
echo -e "\n${YELLOW}Sample entries from compile_commands.json:${NC}"
if command -v jq &> /dev/null; then
    echo "First few entries:"
    jq '.[0:3] | .[] | {file: .file, directory: .directory}' compile_commands.json 2>/dev/null || echo "Could not parse JSON with jq"
else
    echo "Sample file paths:"
    grep '"file":' compile_commands.json | head -5 | sed 's/.*"file": *"\([^"]*\)".*/  \1/'
fi

# Check if build failed but we still got useful data
if [ ${COMPILE_COMMANDS_COUNT} -gt 0 ]; then
    echo -e "\n${GREEN}Great! Even though the build may have failed, we captured ${COMPILE_COMMANDS_COUNT} compilation units.${NC}"
    echo -e "${GREEN}This should be sufficient for clangd to provide good code completion and analysis.${NC}"
else
    echo -e "\n${YELLOW}Warning: No compilation units were captured. The build may have failed too early.${NC}"
fi

echo -e "\n${GREEN}You can now use this compile_commands.json with clangd for better code completion and analysis.${NC}"
echo -e "${YELLOW}Note: Place this file in your project root or configure your editor to find it.${NC}"

# Provide some usage tips
echo -e "\n${YELLOW}Usage tips:${NC}"
echo -e "1. For VS Code: Install the 'clangd' extension and it will automatically use this file"
echo -e "2. For Vim/Neovim: Use coc-clangd or other LSP plugins"
echo -e "3. For Emacs: Use lsp-mode with clangd"
echo -e "4. The file is located at: ${PROJECT_ROOT}/compile_commands.json"
